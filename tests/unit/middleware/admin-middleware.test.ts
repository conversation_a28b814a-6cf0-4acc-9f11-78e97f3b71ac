import { describe, it, expect, beforeEach, jest } from "@jest/globals";
import { NextResponse } from "next/server";
import type { NextFetchEvent } from "next/server";

// Mock next-auth/middleware
jest.mock("next-auth/middleware", () => ({
  withAuth: jest.fn((middleware) => middleware),
}));

// Import the middleware after mocking
import middleware from "@/middleware";

describe("Admin Middleware", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockEvent = (): NextFetchEvent =>
    ({
      waitUntil: jest.fn(),
      passThroughOnException: jest.fn(),
      sourcePage: "",
      request: {} as Request,
      respondWith: jest.fn(),
    }) as NextFetchEvent;

  const createMockRequest = (pathname: string, token: any = null) => {
    const request = {
      nextUrl: { pathname },
      nextauth: { token },
    } as any;
    return request;
  };

  describe("Admin Routes Protection", () => {
    it("should allow access to admin auth routes without authentication", () => {
      const req = createMockRequest("/admin/auth/signin");
      const result = middleware(req, createMockEvent());

      // Should not redirect (NextResponse.next() is called)
      expect(result).toBeDefined();
    });

    it("should redirect unauthenticated users to admin signin", () => {
      const req = createMockRequest("/admin/dashboard");
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307); // Redirect status
      expect(result.headers.get("location")).toBe(
        "http://localhost/admin/auth/signin"
      );
    });

    it("should redirect non-admin users to admin signin", () => {
      const req = createMockRequest("/admin/dashboard", {
        type: "user",
        role: "USER",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe(
        "http://localhost/admin/auth/signin"
      );
    });

    it("should allow admin users to access admin routes", () => {
      const req = createMockRequest("/admin/dashboard", {
        type: "admin",
        role: "ADMIN",
      });
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });

    it("should allow moderator users to access admin routes", () => {
      const req = createMockRequest("/admin/products", {
        type: "admin",
        role: "MODERATOR",
      });
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });

    it("should restrict moderator access to admin-only routes", () => {
      const req = createMockRequest("/admin/admins", {
        type: "admin",
        role: "MODERATOR",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe("http://localhost/admin");
    });

    it("should restrict moderator access to settings", () => {
      const req = createMockRequest("/admin/settings", {
        type: "admin",
        role: "MODERATOR",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe("http://localhost/admin");
    });

    it("should allow admin access to restricted routes", () => {
      const req = createMockRequest("/admin/admins", {
        type: "admin",
        role: "ADMIN",
      });
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });
  });

  describe("User Routes Protection", () => {
    it("should redirect unauthenticated users from protected routes", () => {
      const req = createMockRequest("/profile");
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe(
        "http://localhost/auth/signin"
      );
    });

    it("should allow authenticated users to access protected routes", () => {
      const req = createMockRequest("/profile", {
        type: "user",
        role: "USER",
      });
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });
  });

  describe("Auth Page Redirects", () => {
    it("should redirect authenticated regular users away from auth pages", () => {
      const req = createMockRequest("/auth/signin", {
        type: "user",
        role: "USER",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe("http://localhost/");
    });

    it("should redirect authenticated admin users to admin dashboard from regular auth pages", () => {
      const req = createMockRequest("/auth/signin", {
        type: "admin",
        role: "ADMIN",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe("http://localhost/admin");
    });

    it("should redirect authenticated admin users away from admin auth pages", () => {
      const req = createMockRequest("/admin/auth/signin", {
        type: "admin",
        role: "ADMIN",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe("http://localhost/admin");
    });
  });

  describe("Public Routes", () => {
    const publicRoutes = [
      "/",
      "/products",
      "/categories",
      "/api/products",
      "/api/categories",
      "/api/settings",
      "/_next/static/test.js",
      "/images/test.jpg",
      "/favicon.ico",
    ];

    publicRoutes.forEach((route) => {
      it(`should allow access to public route: ${route}`, () => {
        const req = createMockRequest(route);
        const result = middleware(req, createMockEvent());

        // Should not redirect
        expect(result).toBeDefined();
      });
    });
  });

  describe("API Routes", () => {
    it("should allow access to auth API routes", () => {
      const req = createMockRequest("/api/auth/signin");
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });

    it("should allow access to admin auth API routes", () => {
      const req = createMockRequest("/api/admin/auth/signin");
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });

    it("should protect admin API routes", () => {
      const req = createMockRequest("/api/admin/users");
      const result = middleware(req, createMockEvent());

      // Should require authentication
      expect(result).toBeDefined();
    });
  });

  describe("Edge Cases", () => {
    it("should handle missing token gracefully", () => {
      const req = createMockRequest("/admin/dashboard", null);
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe(
        "http://localhost/admin/auth/signin"
      );
    });

    it("should handle malformed token gracefully", () => {
      const req = createMockRequest("/admin/dashboard", {
        // Missing required fields
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe(
        "http://localhost/admin/auth/signin"
      );
    });

    it("should handle admin token with wrong type", () => {
      const req = createMockRequest("/admin/dashboard", {
        type: "user", // Wrong type
        role: "ADMIN",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe(
        "http://localhost/admin/auth/signin"
      );
    });

    it("should handle user token with admin role (should not happen)", () => {
      const req = createMockRequest("/admin/dashboard", {
        type: "user",
        role: "ADMIN", // This combination should not exist
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe(
        "http://localhost/admin/auth/signin"
      );
    });
  });

  describe("Route Patterns", () => {
    it("should handle nested admin routes", () => {
      const req = createMockRequest("/admin/products/create", {
        type: "admin",
        role: "MODERATOR",
      });
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });

    it("should handle admin route with query parameters", () => {
      const req = createMockRequest("/admin/users?page=2&search=test", {
        type: "admin",
        role: "ADMIN",
      });
      const result = middleware(req, createMockEvent());

      // Should not redirect
      expect(result).toBeDefined();
    });

    it("should handle deeply nested restricted routes for moderators", () => {
      const req = createMockRequest("/admin/admins/create", {
        type: "admin",
        role: "MODERATOR",
      });
      const result = middleware(req, createMockEvent()) as NextResponse;

      expect(result).toBeDefined();
      expect(result.status).toBe(307);
      expect(result.headers.get("location")).toBe("http://localhost/admin");
    });
  });
});
